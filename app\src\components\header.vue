<template>
  <div class="app-header">
    <nav class="navbar">
      <div class="contaniner">
        <div
          class="d-flex justify-space-between align-items-center me-auto pb-0"
          style="column-gap: 170px"
        >
          <router-link to="/home" style="width: 13%">
            <img src="@/assets/images/logo.png" style="width: 100%" alt="" />
          </router-link>
          <div class="flex-1">
            <el-menu
              active-text-color="#333333"
              mode="horizontal"
              text-color="#333333"
              :default-active="activeMenu"
              :router="false"
            >
              <el-menu-item index="/browse" @click="navigateTo('/browse')">
                <svg-icon icon-class="browse" class-name="svg-icon"></svg-icon>
                Data Browsing
              </el-menu-item>
              <el-sub-menu>
                <template #title>
                  <svg-icon
                    icon-class="diversity"
                    class-name="svg-icon"
                  ></svg-icon>
                  Microbial Landscape
                </template>
                <el-menu-item
                  index="/diversity"
                  @click="navigateTo('/diversity')"
                >
                  Diversity Explorer
                </el-menu-item>
                <el-menu-item index="/genomic" @click="navigateTo('/genomic')">
                  Genomic Atlas
                </el-menu-item>
              </el-sub-menu>

              <el-menu-item
                index="/scholarly"
                @click="navigateTo('/scholarly')"
              >
                <svg-icon
                  icon-class="contributors"
                  class-name="svg-icon"
                ></svg-icon>
                Scholarly Archive
              </el-menu-item>
              <el-menu-item
                index="/expert-qa"
                @click="navigateTo('/expert-qa')"
              >
                <svg-icon icon-class="expert" class-name="svg-icon"></svg-icon>
                Expert Q&A
              </el-menu-item>
              <el-sub-menu index="/about">
                <template #title>
                  <svg-icon icon-class="about" class-name="svg-icon"></svg-icon>
                  About
                </template>
                <el-menu-item index="/home" disabled> Manual </el-menu-item>
                <el-menu-item index="/home?slide=5" @click="navigateToHome(5)">
                  Submit to MASH
                </el-menu-item>
                <el-menu-item index="/home?slide=4" @click="navigateToHome(4)">
                  Feature Datasets
                </el-menu-item>
              </el-sub-menu>
            </el-menu>
          </div>
        </div>
      </div>
    </nav>
  </div>
</template>

<script setup>
  import { computed, getCurrentInstance } from 'vue';
  import router from '@/router';

  const { proxy } = getCurrentInstance();

  const activeMenu = computed(() => {
    const { path, query } = proxy.$route;

    // 处理首页上的特殊菜单项
    if (path === '/home' && query.slide) {
      return `/home?slide=${query.slide}`;
    }

    return path;
  });

  const navigateToHome = idx => {
    // 路由跳转到 /home
    router.push({ path: '/home', query: { slide: idx } });
  };

  const navigateTo = path => {
    router.push({
      path: path,
    });
  };
</script>

<style lang="scss" scoped>
  .sub-menu {
    font-weight: 600 !important;
  }

  .menus {
    background-color: #fff;
    //margin-top: 10px;
  }

  :deep(.el-input__wrapper) {
    width: 300px;
    padding: 3px 15px;
    font-size: 0.9em;
    letter-spacing: 1px;
    color: #999;
    outline: none;
    //border: 1px solid #cccccc;
    background: none;
    border-radius: 0;
    margin: 0 8px 0 6px;
    margin-right: 0;
  }

  .search-button {
    background-color: #1e7cb2;
    height: 36px;
    border-radius: 0;
  }

  :deep(.el-sub-menu__title) {
    border-bottom: none !important;
    font-size: 19px;
  }
  .el-popper .el-menu--horizontal .el-menu-item {
    font-size: 17px;
  }
  .is-active.sub-menu,
  .el-popper .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
    background-color: #f1f5f9 !important;
  }

  .app-header {
    //position: fixed;
    top: 30px;
    right: 0;
    left: 0;
    z-index: 1030;

    .navbar {
      background-color: #ffffff;
      z-index: 1000;
      width: 100%;
      //display: flex;
      //align-items: center;
      //justify-content: flex-start;
      //flex-wrap: nowrap;
      transition: all 0.5s ease;
      padding: 0;
      box-shadow: 0 3px 10px 0 rgba(49, 64, 71, 0.08);
      position: fixed;

      .contaniner {
        width: 1640px;
        padding: 0 10px;
        margin: 0 auto;
        max-width: 1640px;

        .navbar-right {
          display: flex;
          align-items: center;
          justify-content: space-between;
          //width: 400px;
        }

        :deep(.el-menu--horizontal.el-menu) {
          justify-content: start;
          border-bottom: none !important;
        }

        .me-auto {
          padding: 15px 0;

          img {
            vertical-align: middle;
          }
        }

        .el-menu--horizontal > .el-menu-item {
          padding-left: 0;
          font-weight: 600;
          font-size: 18px;

          &:hover {
            color: #1e7cb2 !important;
          }

          &:first-child {
            padding-left: 0;
          }

          &:last-child {
            padding-right: 0;
          }

          &.is-active {
            color: #1e7cb2 !important;
            border-bottom: none !important;

            //&:after {
            //  transform: scaleX(1);
            //}
          }
        }

        :deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title) {
          font-weight: 600;
          padding-left: 0;
          .el-menu-item {
            font-size: 17px;
          }
        }

        :deep(.search .el-input__wrapper) {
          border-radius: 8px !important;
        }

        :deep(.search .el-input__prefix) {
          color: #666666 !important;
        }

        .language {
          :deep(.el-input__inner) {
            color: #666666;
            font-weight: 600;
          }

          :deep(.el-input__inner::placeholder),
          :deep(.el-select .el-input .el-select__caret.el-icon) {
            color: #666666;
            font-weight: 600;
          }

          :deep(.el-input__wrapper) {
            max-width: 110px !important;
            box-shadow: none !important;
          }
        }

        .el-divider--vertical {
          height: 58px;
        }
      }
    }
  }

  .el-menu--horizontal .el-menu-item:not(.is-disabled):hover,
  .el-menu--horizontal .el-menu-item:not(.is-disabled):focus {
    background-color: transparent !important;
  }

  .no-border:focus-visible {
    outline: none;
  }

  .login {
    background-color: rgb(255, 255, 255, 0.8);
    padding: 8px 15px;
    color: #020d33;
  }

  .el-menu {
    background-color: transparent;
    justify-content: space-between !important;
  }

  :deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title),
  .el-menu--horizontal > .el-menu-item {
    color: #333 !important;

    &:hover {
      background-color: transparent;
    }
  }

  .register {
    background-color: #175c87;
    border: none;
  }

  .el-menu-item.is-active {
    .svg-icon {
      color: #1e7cb2;
    }
  }

  .svg-icon {
    width: 18px !important;
    height: 18px !important;
    margin-right: 8px;
    min-width: 18px !important;
    min-height: 18px !important;
    flex-shrink: 0 !important;
  }
</style>
