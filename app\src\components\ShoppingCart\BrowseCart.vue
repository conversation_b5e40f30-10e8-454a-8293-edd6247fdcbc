<template>
  <div class="floating-cart-icon" @click="showCartDialog = true">
    <el-icon class="cart-icon">
      <ShoppingCart />
    </el-icon>
    <!-- 数量徽章 -->
    <el-badge :value="999" class="cart-badge" type="danger" />
  </div>

  <!-- 购物车内容弹框 -->
  <el-dialog
    v-model="showCartDialog"
    title="Shopping Cart Groups"
    width="900px"
    :append-to-body="true"
    class="cart-dialog"
  >
    <div class="cart-content">
      <!-- 分组列表 -->
      <div class="groups-container">
        <div
          v-for="(group, index) in cartGroups"
          :key="group.id"
          class="group-item"
        >
          <!-- 分组标签 -->
          <div class="group-label">
            <el-tag size="large" type="info" class="group-tag">
              {{ group.name }} ({{ group.runCount }} Run)
            </el-tag>
          </div>

          <!-- 操作按钮组 -->
          <div class="action-buttons">
            <el-button
              type="primary"
              size="default"
              class="action-btn view-btn"
              @click="viewInDataBrowsing(group)"
            >
              <el-icon><View /></el-icon>
              View in Data Browsing
            </el-button>

            <el-button
              type="success"
              size="default"
              class="action-btn export-btn"
              @click="exportMetaData(group)"
            >
              <el-icon><Download /></el-icon>
              Export meta data
            </el-button>

            <el-button
              type="danger"
              size="default"
              class="action-btn remove-btn"
              @click="removeGroup(group, index)"
            >
              <el-icon><Delete /></el-icon>
              Remove
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="cartGroups.length === 0" class="empty-state">
        <el-empty description="No groups in cart" />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
  import { reactive, ref, computed, nextTick } from 'vue';
  import {
    ShoppingCart,
    View,
    Download,
    Delete,
  } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';

  // 响应式数据
  const showCartDialog = ref(false);

  // 购物车分组数据 (示例数据)
  const cartGroups = reactive([
    {
      id: 1,
      name: 'Group A',
      runCount: 100,
      data: [], // 实际数据
    },
    {
      id: 2,
      name: 'Group B',
      runCount: 50,
      data: [], // 实际数据
    },
  ]);

  // 方法
  const viewInDataBrowsing = group => {
    showCartDialog.value = false;
  };

  const exportMetaData = group => {
    showCartDialog.value = false;
    // 这里可以添加导出数据的逻辑
  };

  const removeGroup = async (group, index) => {
    try {
      await ElMessageBox.confirm(
        `Are you sure you want to remove "${group.name}" from cart?`,
        'Confirm Remove',
        {
          confirmButtonText: 'Remove',
          cancelButtonText: 'Cancel',
          type: 'warning',
        },
      );

      cartGroups.splice(index, 1);
      ElMessage.success(`${group.name} removed from cart`);
    } catch {
      // 用户取消删除
    }
  };
</script>

<style lang="scss" scoped>
  // 弹框样式
  :deep(.cart-dialog) {
    .el-dialog__header {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-bottom: 1px solid #e4e7ed;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }
  }

  // 购物车内容样式
  .cart-content {
    .groups-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .group-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f3f4;
        border-color: #3498db;
        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
      }

      .group-label {
        .group-tag {
          font-size: 16px;
          font-weight: 600;
          padding: 8px 16px;
          border: 2px solid #6c757d;
          background: #fff;
          color: #333;
          border-radius: 6px;

          &:deep(.el-tag__content) {
            font-weight: 600;
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;
        align-items: center;

        .action-btn {
          font-weight: 500;
          border-radius: 6px;
          padding: 8px 16px;
          transition: all 0.2s ease;

          .el-icon {
            margin-right: 6px;
          }

          &.view-btn {
            background: #3498db;
            border-color: #3498db;

            &:hover {
              background: #2980b9;
              border-color: #2980b9;
              transform: translateY(-1px);
            }
          }

          &.export-btn {
            background: #1dc9b7;
            border-color: #1dc9b7;

            &:hover {
              background: #17a2b8;
              border-color: #17a2b8;
              transform: translateY(-1px);
            }
          }

          &.remove-btn {
            background: #f26467;
            border-color: #f26467;

            &:hover {
              background: #c0392b;
              border-color: #c0392b;
              transform: translateY(-1px);
            }
          }
        }
      }
    }

    .empty-state {
      padding: 40px 0;
      text-align: center;
    }
  }

  // Tab容器样式优化
  :deep(.el-tabs__header) {
    margin-bottom: 16px;
  }

  :deep(.el-tabs__item) {
    padding: 0 8px !important;

    &:hover .tab-label .delete-tab-btn {
      opacity: 1;
    }
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }

  .floating-cart-icon {
    position: fixed;
    top: 80%;
    right: 20px;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: #1e7cb2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(30, 124, 178, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;

    &:hover {
      background: #1a6ba0;
      transform: translateY(-50%) scale(1.1);
      box-shadow: 0 6px 16px rgba(30, 124, 178, 0.4);
    }

    .cart-icon {
      font-size: 24px;
      color: white;
    }
  }

  .cart-badge {
    position: absolute;
    top: -5px;
    right: -5px;
  }
  .el-icon {
    flex-shrink: 0 !important;
  }
</style>
