<template>
  <div class="submit-page">
    <div class="container-fluid">
      <el-row :gutter="15" class="mt-1">
        <el-col :span="6">
          <div class="card">
            <el-tabs v-model="form.analysisType">
              <el-tab-pane label="Biogeography" name="Biogeography">
                <el-form
                  :model="form"
                  label-width="auto"
                  style="max-width: 600px"
                  label-position="top"
                >
                  <el-form-item label="Genes Input Type">
                    <el-radio-group v-model="form.geneType">
                      <el-radio value="Gene Name">Gene Name</el-radio>
                      <el-radio value="Orthology Entry"
                        >Orthology Entry</el-radio
                      >
                      <el-radio value="Pathway-KO">Pathway-KO</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <!-- Species Section -->
                  <el-form-item
                    v-if="form.geneType === 'Gene Name'"
                    label="Genes Name"
                    class="mb-2"
                  >
                    <el-select
                      v-model="form.geneName"
                      :teleported="false"
                      filterable
                      multiple
                      placeholder="Select genes names"
                      class="w-100"
                      :max-collapse-tags="3"
                      :multiple-limit="10"
                    >
                      <el-option
                        v-for="it in geneNameOpt"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    v-else-if="form.geneType === 'Orthology Entry'"
                    label="KO ID"
                    class="mb-2"
                  >
                    <el-input
                      v-model="form.koList"
                      type="textarea"
                      placeholder="Enter KO ID (one per line)"
                      raws="6"
                    ></el-input>
                  </el-form-item>
                  <el-form-item v-else label="Pathway Name" class="mb-2">
                    <el-select
                      v-model="form.pathwayKO"
                      :teleported="false"
                      filterable
                      placeholder="Select pathway"
                      class="w-100"
                    >
                      <el-option
                        v-for="it in pathwayNameOpt"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                  </el-form-item>

                  <!-- Data Select  -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label mt-1">
                        <el-icon color="#0080B0" size="14"><Menu /></el-icon>
                        Data Select
                      </div>
                    </template>
                    <!-- Biogeography Mode -->
                    <div class="data-filter-section">
                      <el-form-item label="Longitude" class="mb-2">
                        <el-slider
                          v-model="form.sliderLongitude"
                          range
                          :max="180.0"
                          :min="-180.0"
                          step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="form.longitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="form.longitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item label="Latitude" class="mb-2">
                        <el-slider
                          v-model="form.sliderLatitude"
                          range
                          :max="90"
                          :min="-90"
                          step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="form.latitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="form.latitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item label="Water Body Type" class="mb-2">
                        <el-cascader
                          v-model="form.waterBodyType"
                          :popper-append-to-body="false"
                          :options="waterBodyOpt"
                          :props="props"
                          class="w-100"
                          placeholder="Select"
                        />
                      </el-form-item>

                      <el-form-item
                        class="mb-2"
                        label="Or Select Group from Cart"
                      >
                        <el-select
                          v-model="form.selectedGroups"
                          :teleported="false"
                          multiple
                          placeholder="Select"
                        >
                          <el-option
                            v-for="it in groupOptions"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                          />
                        </el-select>
                      </el-form-item>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      :icon="Promotion"
                      class="w-100 filter-search mt-1"
                      :loading="submitLoading"
                      @click="submitBiogeography"
                      >Submit
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="Function Analysis" name="Function Analysis">
                <el-form
                  :model="form"
                  label-width="auto"
                  style="max-width: 600px"
                  label-position="top"
                >
                  <el-form-item
                    label="Genes Input Type
"
                  >
                    <el-radio-group v-model="form.geneType">
                      <el-radio value="Gene Name">Gene Name</el-radio>
                      <el-radio value="Orthology Entry"
                        >Orthology Entry</el-radio
                      >
                      <el-radio value="Pathway-KO">Pathway-KO</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <!-- Species Section -->
                  <el-form-item
                    v-if="form.geneType === 'Gene Name'"
                    label="Genes Name"
                    class="mb-2"
                  >
                    <el-select
                      v-model="form.geneName"
                      :teleported="false"
                      multiple
                      filterable
                      placeholder="Select genes names"
                      class="w-100"
                      :max-collapse-tags="3"
                    >
                      <el-option
                        v-for="it in geneNameOpt"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    v-else-if="form.geneType === 'Orthology Entry'"
                    label="KO ID"
                    class="mb-2"
                  >
                    <el-input
                      v-model="form.koList"
                      type="textarea"
                      placeholder="Enter KO ID (one per line)"
                      raws="6"
                    ></el-input>
                  </el-form-item>
                  <el-form-item v-else label="Pathway Name" class="mb-2">
                    <el-input v-model="form.pathwayKO"></el-input>
                  </el-form-item>

                  <el-form-item>
                    <template #label>
                      <div class="section-label mt-1">
                        <el-icon color="#0080B0" size="14"><Menu /></el-icon>
                        Data Select
                      </div>
                    </template>
                    <div class="data-filter-section">
                      <div class="mb-1">
                        <el-radio-group v-model="form.type">
                          <el-radio value="From Biota">From Biota</el-radio>
                          <el-radio value="From  Cart">From Cart</el-radio>
                        </el-radio-group>
                      </div>

                      <div
                        v-for="(group, index) in form.functionGroups"
                        :key="group.id"
                        class="diversity-group mb-3"
                      >
                        <div
                          class="group-header d-flex justify-space-between align-items-center mb-2"
                        >
                          <el-input
                            v-model="group.name"
                            class="group-title-input"
                            size="small"
                            :style="{ width: '120px' }"
                          />
                          <div class="group-actions">
                            <el-button
                              v-if="index === form.functionGroups.length - 1"
                              type="success"
                              size="small"
                              round
                              :icon="Plus"
                              @click="addGroup"
                            >
                            </el-button>
                            <el-button
                              v-if="form.functionGroups.length > 1"
                              type="danger"
                              size="small"
                              round
                              :icon="Minus"
                              @click="removeGroup(index)"
                            >
                            </el-button>
                          </div>
                        </div>

                        <div class="group-content">
                          <el-form-item
                            v-if="form.type === 'From Biota'"
                            class="mb-2"
                            label="Water Body Type"
                          >
                            <el-cascader
                              v-model="group.waterBodyType"
                              :options="waterBodyOpt"
                              :props="props"
                              class="w-100"
                              placeholder="Select"
                            />
                          </el-form-item>
                          <el-form-item
                            v-else
                            class="mb-2"
                            label="Select Group from Cart"
                          >
                            <el-select
                              v-model="group.selectedGroups"
                              :teleported="false"
                              placeholder="Select"
                            >
                              <el-option
                                v-for="it in groupOptions"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                              />
                            </el-select>
                          </el-form-item>
                        </div>
                      </div>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      :icon="Promotion"
                      class="w-100 filter-search mt-1"
                      >Submit
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
        <el-col :span="18">
          <div
            v-show="form.analysisType === 'Biogeography'"
            class="card mb-1 pos-relative"
          >
            <div>
              <div class="d-flex justify-space-between align-items-center">
                <h3 class="mb-0 mt-0">
                  <span class="mr-05 font-600"
                    >Global distribution and relativate abundance of</span
                  >

                  <el-select
                    v-model="summarySelect"
                    :teleported="false"
                    filterable
                    placeholder="Select genes names"
                    class="mr-1"
                    style="width: 736px"
                    @change="onKoSelectionChange"
                  >
                    <el-option
                      v-for="ko in koList"
                      :key="ko"
                      :label="ko"
                      :value="ko"
                    />
                  </el-select>
                </h3>
              </div>
              <el-divider class="mt-1"></el-divider>
              <div
                class="map-container"
                :class="{ fullscreen: isMapFullscreen }"
              >
                <div
                  id="genomicMap"
                  style="width: 100%; height: 560px; background-color: #fffff5"
                ></div>
                <!-- 全屏切换按钮 -->
                <el-button
                  class="fullscreen-btn"
                  type="primary"
                  :icon="isMapFullscreen ? 'FullScreen' : 'Rank'"
                  circle
                  :title="isMapFullscreen ? '退出全屏' : '全屏显示'"
                  @click="toggleMapFullscreen"
                >
                </el-button>
              </div>
              <div class="chart-card">
                <!-- 检测/选择样本信息 -->
                <div class="sample-info">
                  <span>Detected/Selected Samples:</span>
                  <span class="sample-count">3/3</span>
                </div>

                <!-- 标准化丰度统计 -->
                <div class="sample-info">
                  <div>Normalized Abundance (Min/Median/Mean/MAX):</div>
                  <div>0.0396/0.0557/0.0534/0.0648</div>
                </div>

                <!-- 图例圆圈 -->
                <div class="legend-container">
                  <div class="legend-item">
                    <div class="size-label">0.0001%</div>
                    <div class="circle" style="width: 7px; height: 7px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.001%</div>
                    <div class="circle" style="width: 11px; height: 11px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.01%</div>
                    <div class="circle" style="width: 17px; height: 17px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.1%</div>
                    <div class="circle" style="width: 21px; height: 21px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">1%</div>
                    <div class="circle" style="width: 25px; height: 25px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">10%</div>
                    <div class="circle" style="width: 30px; height: 30px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">100%</div>
                    <div class="circle" style="width: 35px; height: 35px"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-show="form.analysisType === 'Function Analysis'"
            class="card mb-1"
          >
            <div>
              <img
                src="@/assets/images/hotMap.png"
                style="width: 100%"
                alt=""
              />
            </div>

            <el-table
              :data="analysisData"
              style="width: 100%"
              border
              :header-cell-style="{
                backgroundColor: '#F1F5F9',
                color: '#333333',
                fontWeight: 700,
              }"
            >
              <el-table-column label="KO" prop="ko" width="120">
                <template #default="scope">
                  <a>
                    {{ scope.row.ko }}
                  </a>
                </template>
              </el-table-column>

              <el-table-column
                label="Depth"
                prop="depth"
                width="120"
              ></el-table-column>
              <el-table-column
                label="P-value"
                prop="pValue"
                width="120"
              ></el-table-column>

              <el-table-column label="Pathway" prop="pathway" min-width="200">
                <template #default="scope">
                  <div class="pathway-container">
                    <span
                      v-for="(path, index) in scope.row.pathwayList"
                      :key="index"
                      class="pathway-item"
                    >
                      <a
                        class="link-style"
                        :href="getPathwayUrl(path)"
                        target="_blank"
                      >
                        {{ path }}
                      </a>
                      <span v-if="index < scope.row.pathwayList.length - 1"
                        >,
                      </span>
                    </span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="card">
            <div class="d-flex justify-space-between align-items-center">
              <h3 class="mb-0 mt-0">Sample List</h3>
              <el-popover
                placement="bottom-end"
                :width="300"
                trigger="hover"
                popper-class="metadata-popover"
              >
                <template #reference>
                  <el-button type="success"> Add other metadata </el-button>
                </template>
                <div class="metadata-selector">
                  <h4 class="metadata-title">Select Columns to Display</h4>
                  <div class="column-checkboxes">
                    <el-checkbox
                      v-for="column in allColumns"
                      :key="column.prop"
                      v-model="column.visible"
                      :label="column.label"
                      class="column-checkbox"
                    />
                  </div>
                </div>
              </el-popover>
            </div>
            <el-divider class="mt-1"></el-divider>
            <el-table
              ref="table"
              tooltip-effect="dark"
              :data="dataTable"
              :header-cell-style="{
                backgroundColor: '#F1F5F9',
                color: '#333333',
                fontWeight: 700,
              }"
              border
              :stripe="true"
            >
              <!-- Default visible columns -->
              <el-table-column
                v-if="getColumnVisibility('runId')"
                label="Run ID"
                prop="runId"
                width="200"
              >
                <template #default="scope">
                  <div class="d-flex justify-end">
                    <el-tag
                      v-if="scope.row.group"
                      effect="dark"
                      round
                      :type="getGroupTagType(scope.row.group)"
                      size="small"
                      class="mr-05"
                    >
                      {{ scope.row.group }}
                    </el-tag>
                    {{ scope.row.runId }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                v-if="getColumnVisibility('bioProjectId')"
                label="BioProject ID"
                prop="projectId"
                width="150"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('latitude')"
                label="Latitude"
                prop="latitude"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('longitude')"
                label="Longitude"
                prop="longitude"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('hydrosphereType')"
                label="Hydrosphere Type"
                prop="hydrosphereType"
                width="160"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('geolocation')"
                label="Geolocation"
                prop="geolocation"
                width="150"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('waterBodyType')"
                label="Water Body Type"
                prop="waterBodyType"
                width="160"
              ></el-table-column>

              <!-- Hidden columns that can be toggled -->
              <el-table-column
                v-if="getColumnVisibility('depth')"
                label="Depth"
                prop="depth"
                width="100"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('temperature')"
                label="Temperature"
                prop="temperature"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('salinity')"
                label="Salinity"
                prop="salinity"
                width="100"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('ph')"
                label="pH"
                prop="ph"
                width="80"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('criticalZone')"
                label="Critical Zone"
                prop="criticalZone"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('samplingSubstrate')"
                label="Sampling Substrate"
                prop="samplingSubstrate"
                width="150"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('country')"
                label="Country"
                prop="country"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('waterBodyName')"
                label="Water Body Name"
                prop="waterBodyName"
                width="160"
              ></el-table-column>

              <!-- Analysis Results column (always visible) -->
              <el-table-column
                v-if="getColumnVisibility('analysisResults')"
                label="Analysis Results"
                prop="analysisResults"
                width="160"
                align="center"
              >
                <template #default="scope">
                  <router-link to="/diversity/detail">
                    <div class="text-primary">View</div>
                  </router-link>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              class="mb-1 mt-2 justify-center"
              :page-sizes="[100, 200, 300, 400]"
              layout="total, sizes, prev, pager, next"
              :total="dataTable.length"
            />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>

  <BrowseCart />
</template>

<script setup>
  import { getCurrentInstance, nextTick, onMounted, reactive, ref, toRefs, watch } from 'vue';
  import {
    Plus,
    Minus,
    Promotion,
    Menu,
  } from '@element-plus/icons-vue';
  import L from 'leaflet';
  import 'leaflet/dist/leaflet.css';
  import ocean from '../../../public/geojson/ocean.json';
  import lakes from '../../../public/geojson/sample_lakes.json';
  import rivers from '../../../public/geojson/sample_rivers.json';
  import dataSamples from '@/assets/geojson/biota_analysis_samples.js';
  import BrowseCart from '@/components/ShoppingCart/BrowseCart.vue';
  import {
    createBiogeography,
    getKoList,
    getTableResult,
    getPathKoToNameList,
    getPathwayName,
    getResultMapData,
  } from '@/api/genomic';
  import axios from 'axios';

  const { proxy } = getCurrentInstance();
  const currentPage = ref(1);
  const pageSize = ref(10);
  const table = ref(null);

  // 滑动条数据
  const sliderLatitude = ref([-90, 90]);
  const sliderLongitude = ref([-180.0, 180.0]);

  // 添加地理数据的ref
  const oceanData = ref(null);
  const lakesData = ref(null);
  const riversData = ref(null);
  const geoDataLoading = ref(true);

  // biogeography相关数据
  const biogeographyData = reactive({
    submitLoading: false,
    taskId: '',
    koList: [],
    summarySelect: '',
    geneNameOpt: [],
    pathwayNameOpt: [],
  });

  const { submitLoading, taskId, koList, summarySelect, geneNameOpt, pathwayNameOpt } = toRefs(biogeographyData);

  // 监听滑动条变化，同步到输入框
  watch(sliderLongitude, (newVal) => {
    form.longitudeTo = newVal[0];
    form.longitudeFrom = newVal[1];
  });

  watch(sliderLatitude, (newVal) => {
    form.latitudeTo = newVal[0];
    form.latitudeFrom = newVal[1];
  });

  // 监听输入框变化，同步到滑动条
  watch(() => [form.longitudeTo, form.longitudeFrom], (newVal) => {
    sliderLongitude.value = [newVal[0], newVal[1]];
  });

  watch(() => [form.latitudeTo, form.latitudeFrom], (newVal) => {
    sliderLatitude.value = [newVal[0], newVal[1]];
  });

  const groupOptions = reactive([
    {
      label: 'Group A',
      value: 'Group A',
    },
    {
      label: 'Group B',
      value: 'Group B',
    },
  ]);


  const dataTable = reactive([
    {
      group: 'Group A',
      runId: 'SRR25728811',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      group: 'Group A',
      runId: 'SRR25728812',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      group: 'Group A',
      runId: 'SRR25728814',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      group: 'Group B',
      runId: 'OER00094854',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      group: 'Group B',
      runId: 'SRR28427058',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      group: 'Group B',
      runId: 'SRR16472921',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR23495137',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728903',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728899',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR7663137',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
  ]);

  const form = reactive({
    analysisType: 'Biogeography',
    geneType: 'Gene Name',
    koList: '',
    geneName: [],
    pathwayKO: '',
    sliderLongitude: [-180.0, 180.0],
    sliderLatitude: [-90, 90],
    longitudeTo: -180.0,
    longitudeFrom: 180.0,
    latitudeTo: -90,
    latitudeFrom: 90,
    waterBodyType: '',
    geolocation: '',
    type: 'From Biota',
    selectedGroups: '',
    functionGroups: [
      {
        id: 1,
        name: 'Group A',
        waterBodyType: '',
        geolocation: '',
        selectedGroups: '',
      },
    ],
  });
  const waterBodyOpt = [
    {
      value: 'Inland Water',
      label: 'Inland Water',
      children: [
        {
          value: 'Lakes',
          label: 'Lakes',
        },
        {
          value: 'Rivers',
          label: 'Rivers',
        },
        {
          value: 'Artificial Water Bodies',
          label: 'Artificial Water Bodies',
        },
        {
          value: 'Groundwater',
          label: 'Groundwater',
        },
        {
          value: 'Ice Caps & Glaciers',
          label: 'Ice Caps & Glaciers',
        },
        {
          value: 'Saline lakes',
          label: 'Saline lakes',
        },
        {
          value: 'Soil moisture',
          label: 'Soil moisture',
        },
        {
          value: 'Unclassfied',
          label: 'Unclassfied',
        },
        {
          value: 'Wetlands',
          label: 'Wetlands',
        },
      ],
    },
    {
      value: 'Marine',
      label: 'Marine',
      children: [
        {
          value: 'Pacific Ocean',
          label: 'Pacific Ocean',
        },
        {
          value: 'Atlantic Ocean',
          label: 'Atlantic Ocean',
        },
        {
          value: 'Indian Ocean',
          label: 'Indian Ocean',
        },
        {
          value: 'Southern Ocean ',
          label: 'Southern Ocean ',
        },
        {
          value: 'Arctic Ocean',
          label: 'Arctic Ocean',
        },
        {
          value: 'Mediterranean Sea',
          label: 'Mediterranean Sea',
        },
        {
          value: 'Baltic Sea',
          label: 'Baltic Sea',
        },
      ],
    },
  ];
  const props = {
    expandTrigger: 'hover',
  };

  // 获取分组标签类型
  function getGroupTagType(group) {
    const groupTypes = {
      'Group A': 'primary',
      'Group B': 'warning',
      'Group C': 'success',
      'Group D': 'info',
      'Group E': 'danger',
    };
    return groupTypes[group] || 'info';
  }
  const isMapFullscreen = ref(false);

  const analysisData = reactive([
    {
      ko: 'ko:K00001',
      depth: '8.5085',
      pathway: 'map00010;map00011',
      pValue: '0.03',
      pathwayList: ['ko01230', 'map00010'],
    },
    {
      ko: 'ko:K00002',
      depth: '7.2341',
      pValue: '0.03',
      pathway: 'map00020;map00030;map00040',
      pathwayList: ['ko02060', 'map00520'],
    },
    {
      ko: 'ko:K00003',
      depth: '9.1256',
      pValue: '1',

      pathway: 'map00050',
      pathwayList: ['ko02060', 'map00050'],
    },
  ]);

  //添加分组
  function addGroup() {
    const newGroupId = form.functionGroups.length + 1;
    const groupLetter = String.fromCharCode(64 + newGroupId); // A, B, C, etc.

    form.functionGroups.push({
      id: newGroupId,
      name: `Group ${groupLetter}`,
      sliderLongitude: [-180.0, 180.0],
      sliderLatitude: [-90, 90],
      longitudeTo: -180.0,
      longitudeFrom: 180.0,
      latitudeTo: -90,
      latitudeFrom: 90,
      waterBodyType: '',
      geolocation: '',
      runListText: '',
    });
  }

  // 删除分组
  function removeGroup(index) {
    if (form.functionGroups.length > 1) {
      form.functionGroups.splice(index, 1);
    }
  }

  // 显示隐藏列
  const allColumns = reactive([
    { prop: 'runId', label: 'Run ID', visible: true, default: true },
    {
      prop: 'bioProjectId',
      label: 'BioProject ID',
      visible: true,
      default: true,
    },
    { prop: 'latitude', label: 'Latitude', visible: true, default: true },
    { prop: 'longitude', label: 'Longitude', visible: true, default: true },
    {
      prop: 'hydrosphereType',
      label: 'Hydrosphere Type',
      visible: true,
      default: true,
    },
    { prop: 'geolocation', label: 'Geolocation', visible: true, default: true },
    {
      prop: 'waterBodyType',
      label: 'Water Body Type',
      visible: true,
      default: true,
    },
    {
      prop: 'analysisResults',
      label: 'Analysis Results',
      visible: true,
      default: true,
    },
    { prop: 'depth', label: 'Depth', visible: false, default: false },
    {
      prop: 'temperature',
      label: 'Temperature',
      visible: false,
      default: false,
    },
    { prop: 'salinity', label: 'Salinity', visible: false, default: false },
    { prop: 'ph', label: 'pH', visible: false, default: false },
    {
      prop: 'criticalZone',
      label: 'Critical Zone',
      visible: false,
      default: false,
    },
    {
      prop: 'samplingSubstrate',
      label: 'Sampling Substrate',
      visible: false,
      default: false,
    },
    { prop: 'country', label: 'Country', visible: false, default: false },
    {
      prop: 'waterBodyName',
      label: 'Water Body Name',
      visible: false,
      default: false,
    },
  ]);

  function getColumnVisibility(prop) {
    const column = allColumns.find(col => col.prop === prop);
    return column ? column.visible : false;
  }

  const initMap = id => {
    var latlng = L.latLng(30, 110);

    var map = L.map(id, {
      // crs: L.CRS.Simple,
      center: latlng,
      zoom: 4,
      minZoom: 2, // 设置最小缩放级别为 10
      maxZoom: 18, // 设置最小缩放级别为 10
      // layers: [tiles],
      zoomControl: false,
      attributionControl: false,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });

    map.createPane('oceanPane');
    map.createPane('riverPane');
    map.createPane('pointsPane');

    map.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.getPane('riverPane').style.zIndex = 400; // 河流图层
    map.getPane('pointsPane').style.zIndex = 500; // 圆点图层

    const canvasRenderer = L.canvas({ padding: 0.5 });

    L.geoJSON(ocean, {
      onEachFeature: function (feature, layer) {
        let labelLatLng;
        // 根据特征名称选择标签位置
        if (feature.properties.name === 'North Pacific Ocean') {
          labelLatLng = L.latLng(30, -150);
        } else if (feature.properties.name === 'South Pacific Ocean') {
          labelLatLng = L.latLng(-30, -140);
        } else {
          // 默认使用中心点
          labelLatLng = layer.getBounds().getCenter();
        }

        // 创建一个标记
        var label = L.marker(labelLatLng, {
          icon: L.divIcon({
            className: 'ocean-label',
            html: feature.properties.name,
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
      },

      style: function () {
        return {
          fillColor: '#1C4F80', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // var lakeLayer = null;
    L.geoJSON(lakes, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let zoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: zoom > 4 ? feature.properties.Name : '',
            }),
          );
        });
      },

      style: function () {
        return {
          fillColor: '#9ABAE7', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    L.geoJSON(rivers, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let riverZoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: riverZoom > 4 ? feature.properties.name : '',
            }),
          );
        });
      },
      style: function () {
        return {
          color: '#9ABAE7',
          opacity: 1,
          weight: 1,
          fillOpacity: 1,
          pane: 'riverPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // 创建圆点
    dataSamples.forEach((sample, index) => {
      if (index <= 1000) {
        const size = getSize(sample.abundance);

        const marker = L.circleMarker([sample.lat, sample.lon], {
          radius: size, // 根据丰度设置圆的大小
          fillColor: '#C6A5F4 ',
          color: '#C6A5F4',
          weight: 1,
          opacity: 1,
          fillOpacity: 1,
        });

        let content = `
            Samples:4<br>
            Latitude: ${(parseFloat(sample.lat) || 0).toFixed(2)}<br>
            Longitude: ${(parseFloat(sample.lon) || 0).toFixed(2)}<br>
            Max Abundance: ${sample.abundance}%<br>
          `;

        // 初始绑定工具提示
        marker.bindTooltip(content);

        marker.addTo(map);
      }
    });

    // 保存地图实例到全局变量，用于全屏切换时调整大小
    window.mapInstance = map;
  };

  // 全屏切换方法
  function toggleMapFullscreen() {
    isMapFullscreen.value = !isMapFullscreen.value;
    nextTick(() => {
      setTimeout(() => {
        const mapElement = document.getElementById('genomicMap');
        if (mapElement && window.mapInstance) {
          window.mapInstance.invalidateSize();
        }
      }, 300);
    });
  }

  function getSize(abundance) {
    var size = null;
    if (abundance < 0.0001 || (abundance >= 0.0001 && abundance < 0.001)) {
      size = 4;
    }
    if (abundance >= 0.001 && abundance < 0.01) {
      size = 6;
    }
    if (abundance >= 0.01 && abundance < 0.1) {
      size = 8;
    }
    if (abundance >= 0.1 && abundance < 1) {
      size = 10;
    }
    if (abundance >= 1 && abundance < 10) {
      size = 12;
    }
    if (abundance >= 10 && abundance < 100) {
      size = 14;
    }
    if (abundance >= 100) {
      size = 16;
    }
    return size;
  }

  function getPathwayUrl(path) {
    if (path.startsWith('ko')) {
      return `https://www.genome.jp/entry/${path}`;
    } else {
      return `https://www.kegg.jp/entry/${path}`;
    }
  }

  // 异步获取地理数据的函数
  const fetchGeoData = () => {
    geoDataLoading.value = true;
    // 获取基础路径
    const basePath = import.meta.env.VITE_APP_PUBLIC_PATH || '/';

    Promise.all([
      axios.get(`${basePath}/geojson/ocean.json`),
      axios.get(`${basePath}/geojson/sample_lakes.json`),
      axios.get(`${basePath}/geojson/sample_rivers.json`),
    ])
      .then(([oceanResponse, lakesResponse, riversResponse]) => {
        oceanData.value = oceanResponse.data;
        lakesData.value = lakesResponse.data;
        riversData.value = riversResponse.data;
        return true;
      })
      .catch(error => {
        console.error('加载地理数据失败:', error);
        return false;
      })
      .finally(() => {
        geoDataLoading.value = false;
      });
  };

  // 获取Gene Name下拉框数据
  const fetchGeneNameOptions = () => {
    getPathKoToNameList()
      .then(response => {
        geneNameOpt.value = response.data || [];
      })
      .catch(error => {
        console.error('获取Gene Name选项失败:', error);
      });
  };

  // 获取Pathway Name下拉框数据
  const fetchPathwayNameOptions = () => {
    getPathwayName()
      .then(response => {
        pathwayNameOpt.value = response.data || [];
      })
      .catch(error => {
        console.error('获取Pathway Name选项失败:', error);
      });
  };

  // 监听geneType变化，获取对应的下拉框数据
  watch(() => form.geneType, (newType) => {
    if (newType === 'Gene Name') {
      fetchGeneNameOptions();
    } else if (newType === 'Pathway-KO') {
      fetchPathwayNameOptions();
    }
  }, { immediate: true });

  // 提交biogeography任务
  const submitBiogeography = () => {
    submitLoading.value = true;

    // 构建提交参数
    const params = {
      kos: form.geneType === 'Gene Name' ? form.geneName :
           form.geneType === 'Orthology Entry' ? form.koList.split('\n').filter(ko => ko.trim()) : [],
      pathway: form.geneType === 'Pathway-KO' ? form.pathwayKO : '',
      runIds: [],
      queryDTO: {
        latitudeStart: form.latitudeTo,
        latitudeEnd: form.latitudeFrom,
        longitudeStart: form.longitudeTo,
        longitudeEnd: form.longitudeFrom,
        waterBodyType: Array.isArray(form.waterBodyType) ? form.waterBodyType.join(',') : form.waterBodyType,
        selectedGroups: form.selectedGroups ? [form.selectedGroups] : [],
      }
    };

    createBiogeography(params)
      .then(response => {
        taskId.value = response.data;
        // 任务提交成功后，获取KO列表
        fetchKoList();
        proxy.$modal.msgSuccess('任务提交成功');
      })
      .catch(error => {
        console.error('提交任务失败:', error);
        proxy.$modal.msgError('任务提交失败');
      })
      .finally(() => {
        submitLoading.value = false;
      });
  };

  // 获取KO列表
  const fetchKoList = () => {
    if (!taskId.value) return;

    getKoList({ taskId: taskId.value })
      .then(response => {
        koList.value = response.data || [];

        // 默认选中第一个
        if (koList.value.length > 0) {
          summarySelect.value = koList.value[0];
          // 获取地图数据和表格数据
          fetchMapData();
          fetchTableData();
        }
      })
      .catch(error => {
        console.error('获取KO列表失败:', error);
      });
  };

  // KO选择变化时的处理
  const onKoSelectionChange = () => {
    if (summarySelect.value && taskId.value) {
      fetchMapData();
      fetchTableData();
    }
  };

  // 获取地图数据
  const fetchMapData = () => {
    if (!taskId.value || !summarySelect.value) return;

    getResultMapData({
      taskId: taskId.value,
      ko: summarySelect.value
    })
      .then(response => {
        // 更新地图数据
        updateMapWithData(response.data || []);
      })
      .catch(error => {
        console.error('获取地图数据失败:', error);
      });
  };

  // 获取表格数据
  const fetchTableData = () => {
    if (!taskId.value || !summarySelect.value) return;

    getTableResult({
      taskId: taskId.value,
      ko: summarySelect.value
    })
      .then(response => {
        // 更新表格数据
        if (response.data && response.data.content) {
          dataTable.splice(0, dataTable.length, ...response.data.content);
        }
      })
      .catch(error => {
        console.error('获取表格数据失败:', error);
      });
  };

  // 更新地图数据
  const updateMapWithData = (mapData) => {
    // 这里可以根据mapData更新地图上的点位数据
    // 具体实现需要根据MapDataDTO的结构来调整
    console.log('更新地图数据:', mapData);
  };

  onMounted(() => {
    nextTick(() => {
      initMap('genomicMap');
    });
  });
</script>

<style lang="scss" scoped>
  .container-fluid {
    max-width: 1640px !important;
  }

  .submit-page {
    padding: 120px 0 45px 0;
  }

  :deep(.el-slider__bar),
  .filter-search {
    background-color: #1e7cb2;
  }

  :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
  }

  h3 {
    display: flex;
    align-items: center;
    color: #1e7cb2;
  }

  :deep(.el-popper.is-dark) {
    width: 300px;
  }

  .svg {
    width: 14px;
    height: 14px;
    margin-right: 0.3rem;
  }
  :deep(.el-tabs__item) {
    font-size: 16px;
    font-weight: 600;
    &.is-active {
      color: #0080b0;
    }
  }
  .filter {
    width: 24px;
    height: 26px;
    margin-right: 0.5rem;
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.leaflet-marker-icon.ocean-label) {
    color: #ffffff;
    font-size: 15px;
    width: 200px !important;
    z-index: 200;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.el-upload-dragger) {
    padding: 10px 0;
  }
  :deep(.el-upload-dragger .el-icon--upload) {
    color: #2668b4;
    font-size: 45px;
    margin-bottom: 0;
  }

  :deep(.el-upload-list) {
    margin: 0 !important;
  }

  :deep(.el-button--small.is-round) {
    width: 24px;
    height: 24px;
    font-size: 14px;
    padding: 0;
  }

  .legend {
    //position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }

  .circle {
    background-color: #c6a5f4;
    border-radius: 50%;
  }

  .legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    & > div {
      margin-right: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  h4 {
    text-align: center;
    font-size: 1.1rem;
    margin-right: 20px;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.lake-label) {
    color: #2668b4;
    font-family: initial;
    white-space: nowrap;
  }

  :deep(.leaflet-div-icon) {
    background: transparent;
    border: none;
  }

  /* New styles for the updated form */
  .species-section {
    padding: 0 0 0 16px;
  }

  .species-warning {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 8px;
    line-height: 1.4;
  }

  .data-filter-section {
    padding: 0 4px 0 16px;
    flex: 1;
  }

  .diversity-group {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;
    background-color: #ffffff;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .group-header {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }

  .group-title {
    color: #1e7cb2;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .group-actions {
    display: flex;
  }

  .group-content {
    .el-form-item {
      margin-bottom: 12px;
    }
  }

  .upload-section {
    flex: 1;
    .upload-demo {
      margin-top: 8px;
    }
  }

  .mb-2 {
    margin-bottom: 12px !important;
  }

  .mb-3 {
    margin-bottom: 20px !important;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  :deep(.el-radio) {
    margin-right: 0;
  }

  /* Section label styling */
  .section-label {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1e7cb2;
    padding-bottom: 4px;
    margin-top: 22px !important;
    .el-icon {
      margin-right: 0.2rem;
    }
  }

  /* Custom radio button styling */
  .custom-radio-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    :deep(.el-radio-button) {
      margin-right: 0;
    }

    :deep(.el-radio-button__inner) {
      background-color: #f5f5f5;
      border: 1px solid #dcdfe6;
      color: #606266;
      padding: 4px 10px !important;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e6f7ff;
        border-color: #3498db;
        color: #3498db;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      background-color: #e6f7ff;
      border-color: #3498db;
      color: #3498db;
      box-shadow: none;
    }

    :deep(.el-radio-button:first-child .el-radio-button__inner) {
      border-radius: 4px;
    }

    :deep(.el-radio-button:last-child .el-radio-button__inner) {
      border-radius: 4px;
    }
  }

  /* Group title input styling */
  .group-title-input {
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: none;
      box-shadow: none;
      padding: 0;
    }

    :deep(.el-input__inner) {
      color: #1e7cb2;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
    }
  }

  /* 选中数据信息样式 */
  .selected-data-info {
    margin-top: 8px;
    padding-left: 0;

    .el-tag {
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  /* map */
  .chart-card {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #272728;
    background: rgba(255, 255, 255, 0.9);
    border-left: 1px solid #007ed31a;
    width: auto;
    min-width: 280px;
    padding: 12px;
    border-radius: 8px;
    z-index: 999;
    font-size: 14px;

    // 样本信息样式
    .sample-info {
      margin-bottom: 4px;

      .sample-count {
        color: #0080b0;
        font-weight: 600;
        margin-left: 4px;
      }
    }

    // 图例容器样式
    .legend-container {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      gap: 8px;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px dashed #999;
    }

    // 图例项样式
    .legend-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .size-label {
        text-align: center;
        font-weight: 500;
        white-space: nowrap;
        line-height: 1;
      }

      .circle {
        background: #c3aaf1;
        border-radius: 50%;
      }
    }

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }
  .link-style {
    color: #3498db;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      color: #2980b9;
      text-decoration: underline;
    }

    //&:visited {
    //  color: #8e44ad;
    //}
  }
</style>
