package org.biosino.lf.mash.mashweb.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.config.FileProperties;
import org.biosino.lf.mash.mashweb.core.excepetion.ServiceException;
import org.biosino.lf.mash.mashweb.dto.BiogeographyCreateDTO;
import org.biosino.lf.mash.mashweb.dto.BiogeographyResultQueryDTO;
import org.biosino.lf.mash.mashweb.dto.MapDataDTO;
import org.biosino.lf.mash.mashweb.dto.SamplesQueryDTO;
import org.biosino.lf.mash.mashweb.mongo.entity.Samples;
import org.biosino.lf.mash.mashweb.mongo.repository.SamplesRepository;
import org.biosino.lf.mash.mashweb.util.RuntimeUtils;
import org.biosino.lf.mash.mashweb.util.kegg.KeggEntry;
import org.biosino.lf.mash.mashweb.vo.SamplesBioGeographyVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileFilter;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2025/7/21
 */
@RequiredArgsConstructor
@Service
public class GenomicService {

    private final SamplesRepository samplesRepository;
    private final FileProperties fileProperties;


    public String createBiogeography(BiogeographyCreateDTO paramsDTO) {
        String id = IdUtil.getSnowflakeNextIdStr();

        // 先生成输入文件
        File inputDir = FileUtil.file(fileProperties.getTaskInputDir(id));
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(id));
        File koListFile;
        // 将数据写入input目录
        if (StrUtil.isNotBlank(paramsDTO.getPathway())) {
            List<KeggEntry> entries = CacheService.pathToKeggEntryMap.get(paramsDTO.getPathway());
            List<String> list = entries.stream().map(KeggEntry::getKoId).toList();
            koListFile = FileUtil.writeUtf8Lines(list, FileUtil.file(inputDir, "KO_list.txt"));
        } else {
            koListFile = FileUtil.writeUtf8Lines(paramsDTO.getKos(), FileUtil.file(inputDir, "KO_list.txt"));
        }
        // 如果是条件，就查询
        List<String> selectRunIds;
        if (CollUtil.isNotEmpty(paramsDTO.getRunIds())) {
            selectRunIds = paramsDTO.getRunIds();
        } else {
            SamplesQueryDTO queryDTO = paramsDTO.getQueryDTO();
            selectRunIds = samplesRepository.findDistinctRunId(queryDTO);
        }
        File sampleListFile = FileUtil.writeUtf8Lines(selectRunIds, FileUtil.file(inputDir, "sample_list.txt"));

        String cmd = StrUtil.format("python {} {} {} {} {}",
                fileProperties.getScriptDir() + "/process_ko_data.py",
                fileProperties.getBaseDataDir() + "/All.KO_Sample.wide.RA.tsv",
                koListFile.getAbsolutePath(),
                sampleListFile.getAbsolutePath(),
                outputDir.getAbsolutePath()
        );

        int exitCode = RuntimeUtils.execForExitCode(cmd, id);
        if (exitCode != 0) {
            throw new ServiceException("分析失败");
        }

        // 开始写入结果文件，方便后续
        for (String ko : paramsDTO.getKos()) {
            File file = FileUtil.file(outputDir, ko + ".tsv");
            CsvReader reader = CsvUtil.getReader(CsvReadConfig.defaultConfig().setFieldSeparator('\t'));
            List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);

            // 查询数据
            List<String> runIds = list.stream().map(SamplesBioGeographyVO::getRunId).toList();

            List<Samples> samples = samplesRepository.findByRunIdIn(runIds);

            // 转成runId to Sample Map
            Map<String, Samples> runIdToSampleMap = samples.stream().collect(Collectors.toMap(Samples::getRunId, Function.identity(), (x, y) -> y));

            List<SamplesBioGeographyVO> result = list.stream().peek(x -> {
                if (runIdToSampleMap.containsKey(x.getRunId())) {
                    Samples item = runIdToSampleMap.get(x.getRunId());
                    BeanUtil.copyProperties(item, x);
                }
            }).toList();

            // 将结果写入到结果文件

            File resultFile = FileUtil.file(outputDir, ko + "_result.csv");
            CsvWriter writer = CsvUtil.getWriter(FileUtil.getWriter(resultFile, Charset.defaultCharset(), false));
            writer.writeBeans(result);
            writer.close();
        }

        return id;
    }

    // 根据id 查询结果数据
    public Page<SamplesBioGeographyVO> getTableResult(BiogeographyResultQueryDTO queryDTO) {
        // 读取结果文件中的所有run_list
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(queryDTO.getId()));
        File file = FileUtil.file(outputDir, queryDTO.getKo() + "_result.csv");

        CsvReader reader = CsvUtil.getReader();
        List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);

        // 使用hutool进行内存分页
        Pageable pageable = queryDTO.getPageable();
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), list.size());

        List<SamplesBioGeographyVO> pageContent = CollUtil.sub(list, start, end);

        return new PageImpl<>(pageContent, pageable, list.size());
    }

    public List<MapDataDTO> getMapData(BiogeographyResultQueryDTO queryDTO) {
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(queryDTO.getId()));
        File file = FileUtil.file(outputDir, queryDTO.getKo() + "_result.csv");

        CsvReader reader = CsvUtil.getReader();
        List<SamplesBioGeographyVO> list = reader.read(FileUtil.getUtf8Reader(file), SamplesBioGeographyVO.class);
        Map<String, List<SamplesBioGeographyVO>> collect = list.stream()
                .collect(Collectors.groupingBy(x -> (x.getLongitude() != null ? x.getLongitude() : "-") + StrPool.TAB + (x.getLatitude() != null ? x.getLatitude() : "-")));

        List<MapDataDTO> mapData = new ArrayList<>();
        collect.forEach((k, v) -> {
            String[] split = k.split(StrPool.TAB);
            String longitude = split[0];
            String latitude = split[1];
            List<SamplesBioGeographyVO> dtos = v.stream().toList();
            mapData.add(new MapDataDTO(!longitude.equals("-") ? Double.parseDouble(longitude) : null,
                    !latitude.equals("-") ? Double.parseDouble(latitude) : null, dtos.stream().map(SamplesBioGeographyVO::getValue).max(Comparator.comparing(Double::doubleValue)).orElse(0.0), v.size()));
        });
        return mapData;
    }

    public List<String> getKoList(BiogeographyResultQueryDTO queryDTO) {
        File outputDir = FileUtil.file(fileProperties.getTaskOutputDir(queryDTO.getId()));
        // 列出有多少_result.csv结尾的结果
        List<File> files = FileUtil.loopFiles(outputDir, new FileFilter() {
            @Override
            public boolean accept(File pathname) {
                return pathname.getName().endsWith("_result.csv");
            }
        });
        // files文件名截取掉_result.csv
        List<String> result = new ArrayList<>();
        for (File file : files) {
            String name = file.getName();
            result.add(name.replace("_result.csv", ""));
        }
        return result;
    }
}
